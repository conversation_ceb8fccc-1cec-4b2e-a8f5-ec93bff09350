# ==============================================================================
# 离散 Nelder-Mead 算法实现 - 影响力最大化问题 (优化版本)
# ==============================================================================

import gc
import psutil
import numpy as np
import networkx as nx
import time
import os
from typing import List, Set, Tuple, Optional, Dict
from functools import lru_cache

# 自定义模块导入
from base_fun import IC, gen_graph, local_search, PRE
from NM_fun import (
    centroid_discrete,
    reflect_step, expand_step, contract_outside_step, contract_inside_step,
    degree_initialization, shrink_step, get_operator_statistics, reset_operator_statistics
)
from plot import plot_evolution_results
from save import _save_detailed_results_txt

# ==============================================================================
# 辅助函数和优化类
# ==============================================================================

class FitnessCache:
    """适应度缓存类，用于避免重复计算相同种子集合的适应度"""

    def __init__(self, max_size: int = 10000):
        self.cache: Dict[frozenset, float] = {}
        self.max_size = max_size
        self.hit_count = 0
        self.miss_count = 0

    def get(self, seed_set: Set[int], G: nx.Graph, p: float, neighbors: Dict[int, List[int]], max_hop: int) -> float:
        """获取适应度值，如果缓存中没有则计算并缓存"""
        key = frozenset(seed_set)

        if key in self.cache:
            self.hit_count += 1
            return self.cache[key]

        # 缓存未命中，计算适应度
        self.miss_count += 1
        fitness = PRE(G, seed_set, p, neighbors, max_hop)

        # 如果缓存已满，清除一半的条目（简单的LRU策略）
        if len(self.cache) >= self.max_size:
            items = list(self.cache.items())
            # 保留后一半
            self.cache = dict(items[len(items)//2:])

        self.cache[key] = fitness
        return fitness

    def clear(self):
        """清空缓存"""
        self.cache.clear()
        self.hit_count = 0
        self.miss_count = 0

    def get_stats(self) -> Dict[str, int]:
        """获取缓存统计信息"""
        total = self.hit_count + self.miss_count
        hit_rate = self.hit_count / total if total > 0 else 0
        return {
            'hits': self.hit_count,
            'misses': self.miss_count,
            'hit_rate': hit_rate,
            'cache_size': len(self.cache)
        }

def _summarize_set(s: Set[int]) -> str:
    """集合输出摘要：输出所有节点"""
    arr = sorted(s)
    return f"{arr}"


def _fast_fitness_stats(f_vals: np.ndarray) -> Dict[str, float]:
    """快速计算适应度统计信息"""
    return {
        'min': float(f_vals.min()),
        'max': float(f_vals.max()),
        'mean': float(f_vals.mean()),
        'std': float(f_vals.std())
    }


def get_memory_usage() -> float:
    """
    获取当前进程的内存使用量（MB）

    Returns:
        内存使用量（MB）
    """
    try:
        process = psutil.Process()
        memory_info = process.memory_info()
        return memory_info.rss / 1024 / 1024  # 转换为MB
    except:
        return 0.0


def clear_cache_and_memory(G: nx.Graph, verbose: bool = True) -> None:
    """
    清除缓存和释放内存

    Args:
        G: 网络图对象
        verbose: 是否输出详细信息
    """
    if verbose:
        memory_before = get_memory_usage()

    # 清除图对象的缓存
    if hasattr(G, '_neighbors_cache'):
        del G._neighbors_cache

    # 清除其他可能的缓存属性
    cache_attrs = ['_node_cache', '_edge_cache', '_adj_cache', '_pred_cache', '_succ_cache']
    for attr in cache_attrs:
        if hasattr(G, attr):
            delattr(G, attr)

    # 强制垃圾回收
    collected = gc.collect()

    if verbose:
        memory_after = get_memory_usage()
        memory_freed = memory_before - memory_after
        print("=== 缓存清除完成 ===\n")


# ==============================================================================
# 主算法：离散 Nelder-Mead 算法
# ==============================================================================

def NM(G: nx.Graph, n: int, k: int, p: float, gmax: int,
       alpha: float = 1, gamma: float = 2, rho: float = 1, sigma: float = 0.5,
       max_hop: int = 5, random_seed: Optional[int] = None,
       verbose: bool = True, network_name: str = "unknown") -> Tuple[Set[int], List[float], Dict]:
    """
    离散 Nelder–Mead（DM）算法：选择规模为 k 的种子集合，以最大化 PRE 估计的传播 (优化版本)

    Args:
        G: 网络图
        n: 单纯形"维度"（单纯形规模为 n+1）
        k: 种子集合大小
        p: 传播概率
        gmax: 最大迭代次数
        alpha: 反射系数
        gamma: 扩展系数
        rho: 收缩系数
        sigma: 收缩回退比例
        max_hop: PRE 递推轮数
        random_seed: 随机种子
        verbose: 是否输出详细信息
        network_name: 网络名称，用于结果文件命名

    Returns:
        (最优解集合, 每轮迭代的最优适应度序列, 详细结果信息)
    """

    # 重置算子统计信息
    reset_operator_statistics()

    # 初始化适应度缓存
    fitness_cache = FitnessCache(max_size=5000)

    # 预计算邻接列表，供 PRE 使用（优化：避免重复转换）
    if not hasattr(G, '_neighbors_cache'):
        G._neighbors_cache = {v: list(G.neighbors(v)) for v in G.nodes()}
    neighbors: Dict[int, List[int]] = G._neighbors_cache

    # 初始化单纯形（n+1 个解，每个为大小为 k 的种子集合）
    simplex = degree_initialization(G, n, k, random_seed=random_seed)

    # 使用缓存评估单纯形
    scored_simplex = []
    for s in simplex:
        fitness = fitness_cache.get(s, G, p, neighbors, max_hop)
        scored_simplex.append((fitness, s))
    scored_simplex.sort(key=lambda t: t[0], reverse=True)

    if verbose:
        print("初始化单纯形完成：")
        init_scores = [float(f) for f, _ in scored_simplex]
        print(f"- 初始适应度范围：min={min(init_scores):.4f} max={max(init_scores):.4f} "
              f"均值={np.mean(init_scores):.4f} std={np.std(init_scores):.6f}")
        print(f"- 初始解数量：{len(scored_simplex)}，每个解大小k={k}")

    best_history: List[float] = []

    # 全局最优个体跟踪
    global_best_fitness = float('-inf')
    global_best_solution = None
    global_best_generation = -1

    # 创建结果保存目录（按网络名称分文件夹）
    result_dir = f"result/{network_name}"
    os.makedirs(result_dir, exist_ok=True)

    # 详细结果记录
    detailed_results = {
        'network_name': network_name,
        'parameters': {
            'n': n, 'k': k, 'p': p, 'gmax': gmax,
            'alpha': alpha, 'gamma': gamma, 'rho': rho, 'sigma': sigma, 'max_hop': max_hop
        },
        'generations': [],
        'global_best_history': [],
        'final_best_fitness': None,
        'final_best_solution': None,
        'total_runtime': None
    }

    # 获取算子统计实例
    stats = get_operator_statistics()

    # 主循环：最多 gmax 轮（每轮对所有个体尝试进化）
    for it in range(gmax):
        # 每代局部搜索标志，确保每代最多进行一次局部搜索
        local_search_performed_this_generation = False
        # 按适应度降序排列
        scored_simplex.sort(key=lambda t: t[0], reverse=True)
        f_best, x_best = scored_simplex[0]
        # 优化：预先提取适应度值，避免重复列表推导
        f_vals = np.array([f for f, _ in scored_simplex], dtype=np.float64)

        # 更新全局最优个体（确保适应度不会变差）
        if f_best > global_best_fitness:
            global_best_fitness = f_best
            global_best_solution = x_best.copy() if hasattr(x_best, 'copy') else set(x_best)
            global_best_generation = it
            if verbose:
                print(f"*** 发现新的全局最优解！代数={it}, 适应度={f_best:.4f} ***")

        # 精英保留策略：确保全局最优解始终在单纯形中
        # 检查当前单纯形是否包含全局最优解
        global_best_in_simplex = False
        for _, x_i in scored_simplex:
            if x_i == global_best_solution:
                global_best_in_simplex = True
                break

# ====================================================================================================
        # 如果全局最优解不在当前单纯形中，替换最差解
        if not global_best_in_simplex:
            scored_simplex[-1] = (global_best_fitness, global_best_solution)
            scored_simplex.sort(key=lambda t: t[0], reverse=True)
            if verbose:
                print(f"*** 精英保留：将全局最优解重新加入单纯形 ***")
# ====================================================================================================

        # 记录全局最优适应度（确保单调递增）
        best_history.append(float(global_best_fitness))

        # 记录详细的代信息
        generation_info = {
            'generation': it,
            'current_best_fitness': float(f_best),
            'global_best_fitness': float(global_best_fitness),
            'fitness_stats': _fast_fitness_stats(f_vals),
            'is_global_best_updated': f_best > global_best_fitness
        }
        detailed_results['generations'].append(generation_info)
        detailed_results['global_best_history'].append(float(global_best_fitness))

        if verbose:
            f_worst = scored_simplex[-1][0]
            print(f"\n【迭代 {it}】全局最优值={global_best_fitness:.4f} 当前单纯形最优值={f_best:.4f} 最差值={f_worst:.4f} "
                  f"标准差={f_vals.std():.6f}")

            # 显示前3和后3的适应度
            head = ", ".join([f"{scored_simplex[i][0]:.4f}"
                             for i in range(min(3, len(scored_simplex)))])
            tail = ", ".join([f"{scored_simplex[-i-1][0]:.4f}"
                             for i in range(min(3, len(scored_simplex)))][::-1])
            print(f"- 前3适应度：[{head}]  后3适应度：[{tail}]")
            print(f"- 全局最优解：{_summarize_set(set(global_best_solution))}")
            if f_best != global_best_fitness:
                print(f"- 当前单纯形最优解：{_summarize_set(set(x_best))}")

        # 基于当前单纯形，对每个个体独立计算（排除自身）质心并完成一轮进化
        current_scored = list(scored_simplex)
        new_candidates: List[Tuple[float, Set[int]]] = []
        improved_count = 0

        # 预计算所有个体的集合，避免重复转换
        all_sets = [s for _, s in current_scored]

        # 记录收缩失败的个体数量
        contract_failed_count = 0

        # 预计算所有个体的阈值，避免重复计算
        thresholds = []
        for j in range(len(current_scored)):
            if j == 0:
                others_scores = [current_scored[i][0] for i in range(1, len(current_scored))]
            elif j == len(current_scored) - 1:
                others_scores = [current_scored[i][0] for i in range(len(current_scored) - 1)]
            else:
                others_scores = [current_scored[i][0] for i in range(len(current_scored)) if i != j]
            thresholds.append(min(others_scores) if others_scores else current_scored[j][0])

        # 预计算所有质心，避免重复计算
        centroids = {}
        direction_vectors = {}
        for j, (f_j, x_j) in enumerate(current_scored):
            x_c = centroid_discrete(G, all_sets, exclude_set=x_j, k=k)
            centroids[j] = x_c
            direction_vectors[j] = x_c - x_j

        # 对每个个体进行进化操作
        for j, (f_j, x_j) in enumerate(current_scored):
            # 使用预计算的质心和方向向量
            x_c = centroids[j]
            D = direction_vectors[j]

            # 若 D 为空或质心与自身重合，跳过该个体的进化操作
            if len(D) == 0:
                candidate = (f_j, x_j)  # 保持原个体不变
            else:
                # 反射操作
                x_r = reflect_step(G, x_c, x_j, alpha=alpha, k=k, verbose=False, stats=stats)
                f_r = fitness_cache.get(x_r, G, p, neighbors, max_hop)

                # 使用预计算的阈值
                f_threshold = thresholds[j]

                # 根据反射结果决定下一步操作
                candidate: Tuple[float, Set[int]]

                if f_r > f_best:
                    # 反射成功（超过最优解）
                    stats.record_reflect(True)

                    # 扩展操作：计算从质心到反射点的方向向量
                    expand_direction = x_r - x_c  # 扩展方向：从质心指向反射点
                    x_e = expand_step(G, x_r, expand_direction, gamma=gamma, k=k, verbose=False, stats=stats)
                    f_e = fitness_cache.get(x_e, G, p, neighbors, max_hop)
                    if f_e > f_r:
                        # 扩展成功
                        stats.record_expand(True)
                        candidate = (f_e, x_e)

                        # 扩展成功后对全局最优解进行局部搜索（每代最多一次）
                        if not local_search_performed_this_generation:
                            if verbose:
                                print(f"*** 扩展成功！对全局最优解进行局部搜索优化 ***")

                            # 获取当前全局最优解
                            current_global_best = global_best_solution
                            if current_global_best is not None:
                                # 计算局部搜索前的适应度
                                original_fitness = fitness_cache.get(current_global_best, G, p, neighbors, max_hop)

                                # 执行局部搜索
                                optimized_solution = local_search(current_global_best, G, p, k, neighbors, max_hop)
                                optimized_solution_set = set(optimized_solution)

                                # 计算局部搜索后的适应度
                                optimized_fitness = fitness_cache.get(optimized_solution_set, G, p, neighbors, max_hop)

                                # 如果局部搜索找到了更好的解，更新全局最优解
                                if optimized_fitness > original_fitness:
                                    global_best_fitness = optimized_fitness
                                    global_best_solution = optimized_solution_set
                                    global_best_generation = it
                                    if verbose:
                                        improvement = optimized_fitness - original_fitness
                                        print(f"*** 局部搜索成功！适应度从 {original_fitness:.4f} 提升到 {optimized_fitness:.4f} (提升: {improvement:.4f}) ***")
                                elif verbose:
                                    print(f"局部搜索未找到更好解，保持原解 (适应度: {original_fitness:.4f})")

                                # 标记本代已进行局部搜索
                                local_search_performed_this_generation = True
                        elif verbose:
                            print(f"*** 扩展成功，但本代已进行过局部搜索，跳过 ***")
                    else:
                        # 扩展失败
                        stats.record_expand(False)
                        candidate = (f_r, x_r)
                elif f_r > f_threshold:
                    # 反射成功（超过阈值）
                    stats.record_reflect(True)
                    # 接受反射结果
                    candidate = (f_r, x_r)
                else:
                    # 反射失败
                    stats.record_reflect(False)

                    # 收缩操作
                    contract_success = False
                    if f_r > f_j:
                        # 外部收缩
                        x_co = contract_outside_step(G, x_c, x_j, rho=rho, k=k, verbose=False, stats=stats)
                        f_co = fitness_cache.get(x_co, G, p, neighbors, max_hop)
                        if f_co > f_j:
                            # 外部收缩成功
                            stats.record_contract_outside(True)
                            candidate = (f_co, x_co)
                            contract_success = True
                        else:
                            # 外部收缩失败
                            stats.record_contract_outside(False)
                            candidate = (f_j, x_j)
                    else:
                        # 内部收缩
                        x_ci = contract_inside_step(G, x_j, x_c, rho=rho, k=k, verbose=False, stats=stats)
                        f_ci = fitness_cache.get(x_ci, G, p, neighbors, max_hop)
                        if f_ci > f_j:
                            # 内部收缩成功
                            stats.record_contract_inside(True)
                            candidate = (f_ci, x_ci)
                            contract_success = True
                        else:
                            # 内部收缩失败
                            stats.record_contract_inside(False)
                            candidate = (f_j, x_j)

                    # 记录收缩失败的个体
                    if not contract_success:
                        contract_failed_count += 1

            # 统计改进个体数量
            if candidate[0] > f_j:
                improved_count += 1

            new_candidates.append(candidate)

        # 计算需要收缩的个体总数（即D不为空的个体数）- 使用预计算的方向向量
        need_contract_count = sum(1 for j in range(len(current_scored)) if len(direction_vectors[j]) > 0)

        # 检测收缩失败并执行回退操作
        # 若所有需要收缩的操作均失败，执行收缩回退
        if need_contract_count > 0 and contract_failed_count == need_contract_count:
            if verbose:
                print(f"*** 检测到收缩均失败（失败数：{contract_failed_count}/{need_contract_count}），执行收缩回退操作 ***")
            # 使用shrink_step进行收缩回退
            new_simplex_sets = shrink_step(G, current_scored, k, sigma, verbose=verbose, stats=stats)
            # 使用缓存评估收缩后的单纯形
            scored_simplex = []
            for s in new_simplex_sets:
                fitness = fitness_cache.get(s, G, p, neighbors, max_hop)
                scored_simplex.append((fitness, s))
            scored_simplex.sort(key=lambda t: t[0], reverse=True)
        else:
            # 统一评估整个人口
            if verbose:
                print(f"- 本轮改进个体数：{improved_count}/{len(current_scored)}")
                if contract_failed_count > 0:
                    print(f"- 收缩失败个体数：{contract_failed_count}/{need_contract_count}")
            # 使用缓存评估新候选解
            scored_simplex = []
            for _, s in new_candidates:
                fitness = fitness_cache.get(s, G, p, neighbors, max_hop)
                scored_simplex.append((fitness, s))
            scored_simplex.sort(key=lambda t: t[0], reverse=True)

        if verbose:
            f_vals_new = [f for f, _ in scored_simplex]
            f_vals_array = np.array(f_vals_new)  # 避免重复转换
            print(f"- 轮末适应度范围：min={f_vals_array.min():.4f} max={f_vals_array.max():.4f} "
                  f"均值={f_vals_array.mean():.4f} std={f_vals_array.std():.6f}")
            f_best_new, x_best_new = max(scored_simplex, key=lambda t: t[0])
            print(f"- 更新后新最优值={f_best_new:.4f}，最优解：{_summarize_set(x_best_new)}")

    # 达到迭代上限，使用全局最优解作为最终结果
    if verbose:
        print("达到最大迭代次数，返回全局最优解。")
        print(f"全局最优适应度：{global_best_fitness:.4f} (发现于第{global_best_generation}代)")
        print(f"全局最优解：{_summarize_set(global_best_solution)}")

        # 输出算子统计信息
        stats.print_statistics()

        # 输出缓存统计信息
        cache_stats = fitness_cache.get_stats()
        print(f"\n=== 适应度缓存统计 ===")
        print(f"缓存命中次数: {cache_stats['hits']}")
        print(f"缓存未命中次数: {cache_stats['misses']}")
        print(f"缓存命中率: {cache_stats['hit_rate']:.2%}")
        print(f"缓存大小: {cache_stats['cache_size']}")
        total_evaluations = cache_stats['hits'] + cache_stats['misses']
        saved_evaluations = cache_stats['hits']
        print(f"节省的适应度计算次数: {saved_evaluations}/{total_evaluations}")
        if total_evaluations > 0:
            print(f"计算效率提升: {saved_evaluations/total_evaluations:.2%}")

    # 确保返回的是set类型
    if not isinstance(global_best_solution, set):
        global_best_solution = set(global_best_solution)

    # 完善详细结果记录
    detailed_results['final_best_fitness'] = float(global_best_fitness)
    detailed_results['final_best_solution'] = sorted(list(global_best_solution))
    detailed_results['global_best_generation'] = global_best_generation

    # 添加算子统计信息到详细结果
    detailed_results['operator_statistics'] = {
        'summary': stats.get_summary(),
        'success_rates': stats.get_success_rates()
    }

    # 添加缓存统计信息到详细结果
    detailed_results['cache_statistics'] = fitness_cache.get_stats()

    # 清除缓存和图对象的邻接列表缓存
    fitness_cache.clear()
    if hasattr(G, '_neighbors_cache'):
        del G._neighbors_cache

    # 强制垃圾回收
    _ = gc.collect()

    return global_best_solution, best_history, detailed_results

# ==============================================================================
# 主函数
# ==============================================================================

def main():
    """
    主函数：运行离散NM算法求解影响力最大化问题

    该函数执行完整的算法流程：
    1. 加载网络数据
    2. 运行离散Nelder-Mead算法
    3. 执行局部搜索优化
    4. 评估最终结果
    5. 生成可视化图表
    6. 保存结果文件
    7. 清除缓存和释放内存
    """
    start_time = time.time()
    initial_memory = get_memory_usage()

    print("=" * 60)
    print("离散Nelder-Mead算法 - 影响力最大化问题求解")
    print("=" * 60)
    print(f"程序启动时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"初始内存使用: {initial_memory:.2f} MB")

    # ==================== 网络数据路径配置 ====================
    # 可选择的网络数据文件路径
    # network_path = "D:\\VS\\code\\networks\\netscience.txt"
    # network_path = "D:\\VS\\code\\networks\\blog.txt"
    # network_path = "D:\\VS\\code\\networks\\pgp.txt"
    # network_path = "D:\\VS\\code\\networks\\CA-HepTh.txt"
    # network_path = "D:\\VS\\code\\networks\\NetHEHT.txt"
    network_path = "D:\\VS\\code\\networks\\deezer.txt"

    # 提取网络名称
    network_name = os.path.splitext(os.path.basename(network_path))[0]

    # ==================== 加载网络数据 ====================
    print(f"正在加载网络数据: {network_path}")
    g = gen_graph(network_path)
    p = 0.05  # 传播概率

    # ==================== 算法参数配置 ====================
    # 基本参数
    n = 30        # 单纯形“维度”（单纯形规模为 n+1）
    k = 100       # 种子集合大小
    gmax = 300    # 最大迭代次数
    max_hop = 5   # PRE 递推轮数

    # Nelder-Mead算法系数（推荐配置）
    alpha = 0.5     # 反射系数
    gamma = 1.5     # 扩展系数
    rho = 1.0       # 收缩系数
    sigma = 0.375   # 收缩回退比例


    print(f"开始运行离散NM算法 - 网络: {network_name}, k={k}")
    print(f"网络规模: {g.number_of_nodes()}个节点, {g.number_of_edges()}条边")

    # ==================== 运行离散NM算法 ====================
    print(f"\n=== 第一阶段：离散Nelder-Mead算法优化 ===")
    seed, best_history, detailed_results = NM(
        g, n, k, p, gmax,
        alpha=alpha, gamma=gamma, rho=rho, sigma=sigma,
        max_hop=max_hop, verbose=True, network_name=network_name
    )

    # ==================== 局部搜索优化 ====================
    print(f"\n=== 第二阶段：局部搜索优化 ===")

    # 预计算邻接列表，供局部搜索使用
    if not hasattr(g, '_neighbors_cache'):
        g._neighbors_cache = {v: list(g.neighbors(v)) for v in g.nodes()}
    neighbors = g._neighbors_cache

    # 创建局部搜索专用的适应度缓存
    local_search_cache = FitnessCache(max_size=1000)

    # 计算局部搜索前的PRE适应度
    original_fitness = local_search_cache.get(seed, g, p, neighbors, max_hop)
    print(f"局部搜索前PRE适应度: {original_fitness:.6f}")
    print(f"局部搜索前种子集合: {sorted(seed)}")

    # 执行局部搜索
    optimized_seed = local_search(seed, g, p, k, neighbors, max_hop)
    optimized_seed_set = set(optimized_seed)

    # 计算局部搜索后的PRE适应度
    optimized_fitness = local_search_cache.get(optimized_seed_set, g, p, neighbors, max_hop)
    print(f"局部搜索后PRE适应度: {optimized_fitness:.6f}")
    print(f"局部搜索后种子集合: {sorted(optimized_seed)}")

    # 输出局部搜索阶段的缓存统计
    local_cache_stats = local_search_cache.get_stats()
    print(f"局部搜索缓存统计: 命中率 {local_cache_stats['hit_rate']:.2%}, "
          f"节省计算 {local_cache_stats['hits']}/{local_cache_stats['hits'] + local_cache_stats['misses']}")

    # 清除局部搜索缓存
    local_search_cache.clear()

    # 比较优化效果
    fitness_improvement = optimized_fitness - original_fitness
    print(f"PRE适应度提升: {fitness_improvement:.6f} ({fitness_improvement/original_fitness*100:.2f}%)")

    # 如果局部搜索找到了更好的解，则使用优化后的种子集合
    if optimized_fitness > original_fitness:
        print("*** 局部搜索找到了更好的解，使用优化后的种子集合 ***")
        seed = optimized_seed_set
        # 更新详细结果中的最优解信息
        detailed_results['final_best_fitness'] = float(optimized_fitness)
        detailed_results['final_best_solution'] = sorted(list(optimized_seed_set))
        detailed_results['local_search_applied'] = True
        detailed_results['local_search_improvement'] = float(fitness_improvement)
    else:
        print("局部搜索未找到更好的解，保持原解")
        detailed_results['local_search_applied'] = False
        detailed_results['local_search_improvement'] = 0.0

    # ==================== 最终影响力评估 ====================
    print(f"\n=== 第三阶段：蒙特卡洛IC模型评估 ===")
    mc = 1000  # 蒙特卡洛模拟次数
    influence = IC(g, seed, p, mc)

    end_time = time.time()
    runtime = end_time - start_time

    # 更新详细结果中的运行时间和IC评估
    detailed_results['total_runtime'] = runtime
    detailed_results['ic_evaluation'] = {
        'monte_carlo_runs': mc,
        'estimated_influence': float(influence)
    }

    # ==================== 输出最终结果 ====================
    print(f"\n=== 最终结果汇总 ===")
    print(f"网络: {network_name}")
    print(f"种子集合大小: {k}")
    print(f"全局最优适应度: {detailed_results['final_best_fitness']:.6f}")
    print(f"IC模型估计影响力: {influence:.2f}")
    print(f"运行时间: {runtime:.2f}秒")
    print(f"最优种子集合: {sorted(seed)}")

    # ==================== 生成可视化结果 ====================
    # print(f"\n=== 第四阶段：生成可视化结果 ===")
    # 创建结果目录（按网络名称分文件夹）
    result_dir = f"result/{network_name}"
    os.makedirs(result_dir, exist_ok=True)

    # print("正在生成可视化图表...")

    # 生成主要的进化曲线图（最优个体适应度变化和标准差变化）
    evolution_plot = plot_evolution_results(best_history, detailed_results, network_name, k, result_dir)
    print(f"- 已保存进化曲线到: {evolution_plot}")

    # ==================== 保存详细结果 ====================
    # 保存完整的详细结果（TXT格式）
    result_file = os.path.join(result_dir, f"detailed_results_k{k}.txt")
    _save_detailed_results_txt(detailed_results, result_file)

    print(f"所有结果已保存到目录: {result_dir}")

    # ==================== 最终清理和性能统计 ====================
    # 最终清除所有缓存和释放内存
    clear_cache_and_memory(g, verbose=True)



if __name__ == "__main__":
    main()