import numpy as np
import networkx as nx
import random
from collections import Counter
from typing import List, Set, Tuple, Dict
import matplotlib.pyplot as plt


def gen_graph(filename: str) -> nx.Graph:
    """
    从文件加载图形数据并生成无向图
    
    Args:
        filename: 包含边列表的文件路径
        
    Returns:
        nx.Graph: 生成的无向图对象
        
    Raises:
        RuntimeError: 当图加载失败时
    """
    try:
        G = nx.Graph()  # 创建一个空的无向图
        edges_data = np.loadtxt(filename, skiprows=1, usecols=[0, 1])  # 读取边数据
        edges = [(int(u), int(v)) for u, v in edges_data]
        G.add_edges_from(edges)  # 将边添加到图中
        return G  # 返回生成的图
    except Exception as e:
        raise RuntimeError(f"加载图形错误: {e}")
    

# 影响力传播模拟 (IC模型)
def IC(g, seed, p, mc=1000):
    seed = set(seed)  # 转换为集合，避免重复元素
    influence = []
    neighbors_of = g.neighbors  # 绑定局部以减少属性查找
    rand = np.random.random
    for _ in range(mc):
        new_active, last_active = set(seed), set(seed)  # 使用集合来去重
        while new_active:
            new_ones = set()
            for node in new_active:
                for neighbor in neighbors_of(node):  # 直接迭代邻居，避免 list 转换
                    if rand() < p:
                        new_ones.add(neighbor)
            new_active = new_ones - last_active
            last_active.update(new_active)
        influence.append(len(last_active))  # 记录激活的总节点数
    return np.mean(influence)  # 返回平均影响力


# ---------------------------
# PRE 近似影响力估计
# ---------------------------

def PRE(G: nx.Graph, S: Set[int], p: float, neighbors: Dict[int, List[int]], max_hop: int = 5) -> float:
# def PRE_vectorized(G: nx.Graph, S: Set[int], p: float, neighbors: Dict[int, List[int]], max_hop: int = 5) -> float:
    """
    PRE近似影响力估计（向量化版本）
    """
    S = set(S)

    # 缓存节点列表
    if not hasattr(G, "_node_list_cache"):
        setattr(G, "_node_list_cache", list(G.nodes()))
        setattr(G, "_node_index_cache", {v: i for i, v in enumerate(G.nodes())})
    nodes: List[int] = getattr(G, "_node_list_cache")
    node_index: Dict[int, int] = getattr(G, "_node_index_cache")

    n = len(nodes)
    P = np.zeros(n, dtype=np.float64)
    idx_S = [node_index[v] for v in S]
    P[idx_S] = 1.0  # 种子初始概率为1

    # 预构建邻接表为稀疏矩阵（只构建一次，避免重复开销）
    if not hasattr(G, "_adj_matrix_cache"):
        row, col = [], []
        for v in nodes:
            for u in neighbors.get(v, []):
                row.append(node_index[v])
                col.append(node_index[u])
        # 邻接矩阵 (n x n)，存储为稀疏CSR
        import scipy.sparse as sp
        setattr(G, "_adj_matrix_cache", sp.csr_matrix((np.ones(len(row)), (row, col)), shape=(n, n)))
    A = getattr(G, "_adj_matrix_cache")

    for _ in range(max_hop):
        # 计算 1 - Π(1 - p * P[u])  —— 用矩阵乘法近似
        AP = A.dot(P)  # 每个节点的邻居概率和
        new_P = 1.0 - np.exp(np.log1p(-p) * AP)  # ≈ 连乘展开
        new_P[idx_S] = 1.0  # 种子保持 1
        P = new_P

    return float(np.sum(P))


# def local_search(xi, G, p, k, neighbors=None, max_hop=5):
#     """
#     局部搜索算法，优化种子节点集合

#     Args:
#         xi: 当前种子集合
#         G: NetworkX 图对象
#         p: 传播概率
#         k: 种子集合大小
#         neighbors: 邻居字典，格式为 {node: [neighbors]}
#         max_hop: PRE递推轮数

#     Returns:
#         list: 优化后的种子集合
#     """
#     # 如果没有提供neighbors字典，则生成一个
#     if neighbors is None:
#         neighbors = {v: list(G.neighbors(v)) for v in G.nodes()}

#     # 确保xi是集合类型
#     if not isinstance(xi, set):
#         xi = set(xi)

#     # 缓存当前种子集合的影响力值
#     xi_fitness = PRE(G, xi, p, neighbors, max_hop)

#     for x_ij in list(xi):  # 遍历当前种子节点的副本
#         node_neighbors = list(G.neighbors(x_ij))  # 获取当前节点的邻居
#         for neighbor in node_neighbors:  # 遍历邻居节点
#             if neighbor not in xi:  # 如果邻居不在当前种子节点中
#                 # 尝试替换
#                 xi_new = xi.copy()  # 创建当前种子节点的副本
#                 xi_new.remove(x_ij)  # 从副本中移除当前节点
#                 xi_new.add(neighbor)  # 添加邻居节点

#                 # 只在影响力提高时才进行更新
#                 xi_new_fitness = PRE(G, xi_new, p, neighbors, max_hop)
#                 if xi_new_fitness > xi_fitness:
#                     xi = xi_new  # 更新种子节点
#                     xi_fitness = xi_new_fitness  # 更新当前种子集合的影响力值
#                     break  # 退出邻居循环，尝试对下一个种子节点优化

#     return list(xi)  # 返回优化后的种子节点列表

from joblib import Parallel, delayed
import time

# def local_search(xi, G, p, k=None, neighbors=None, max_hop=5, max_iterations=50,
#                  convergence_threshold=1e-6, parallel_threshold=20, verbose=False):
#     """
#     优化的局部搜索算法，解决效率问题

#     Args:
#         xi: 当前种子集合 (list 或 set)
#         G: NetworkX 图对象
#         p: 传播概率
#         k: 种子集合大小
#         neighbors: 邻居字典，格式为 {node: [neighbors]}，可提前计算加速
#         max_hop: PRE递推轮数
#         max_iterations: 最大迭代次数，防止无限循环
#         convergence_threshold: 收敛阈值，当改进小于此值时停止
#         parallel_threshold: 候选解数量超过此值时才使用并行化
#         verbose: 是否输出详细信息

#     Returns:
#         list: 优化后的种子集合
#     """
#     start_time = time.time()

#     # 如果没有提供neighbors字典，则生成一个
#     if neighbors is None:
#         neighbors = {v: list(G.neighbors(v)) for v in G.nodes()}

#     # 确保xi是集合类型
#     if not isinstance(xi, set):
#         xi = set(xi)

#     # 缓存当前种子集合的影响力值
#     xi_fitness = PRE(G, xi, p, neighbors, max_hop)
#     original_fitness = xi_fitness

#     if verbose:
#         print(f"局部搜索开始，初始适应度: {xi_fitness:.6f}")

#     iteration = 0
#     consecutive_no_improvement = 0
#     max_no_improvement = 5  # 连续无改进次数上限

#     while iteration < max_iterations and consecutive_no_improvement < max_no_improvement:
#         iteration += 1

#         # 构造候选解 - 使用生成器减少内存使用
#         def generate_candidates():
#             for x_ij in xi:
#                 node_neighbors = neighbors.get(x_ij, [])
#                 for neighbor in node_neighbors:
#                     if neighbor not in xi:
#                         xi_new = xi.copy()
#                         xi_new.remove(x_ij)
#                         xi_new.add(neighbor)
#                         yield (xi_new, x_ij, neighbor)

#         candidates = list(generate_candidates())

#         if not candidates:
#             if verbose:
#                 print(f"第{iteration}轮：无可用候选解，搜索结束")
#             break

#         if verbose:
#             print(f"第{iteration}轮：评估{len(candidates)}个候选解")

#         # 根据候选解数量决定是否使用并行化
#         if len(candidates) >= parallel_threshold:
#             # 并行评估候选解
#             try:
#                 results = Parallel(n_jobs=-1, backend='threading')(
#                     delayed(PRE)(G, cand[0], p, neighbors, max_hop) for cand in candidates
#                 )
#             except Exception as e:
#                 if verbose:
#                     print(f"并行计算失败，回退到串行计算: {e}")
#                 # 回退到串行计算
#                 results = [PRE(G, cand[0], p, neighbors, max_hop) for cand in candidates]
#         else:
#             # 串行评估候选解（避免并行开销）
#             results = [PRE(G, cand[0], p, neighbors, max_hop) for cand in candidates]

#         # 找到最优候选
#         best_idx = max(range(len(results)), key=lambda i: results[i])
#         best_fit = results[best_idx]
#         improvement = best_fit - xi_fitness

#         # 检查是否有显著改进
#         if improvement > convergence_threshold:
#             xi = candidates[best_idx][0]
#             xi_fitness = best_fit
#             consecutive_no_improvement = 0

#             if verbose:
#                 print(f"第{iteration}轮：找到改进解，适应度: {xi_fitness:.6f} (提升: {improvement:.6f})")
#         else:
#             consecutive_no_improvement += 1
#             if verbose:
#                 print(f"第{iteration}轮：无显著改进 (最佳提升: {improvement:.6f})")

#     end_time = time.time()
#     total_improvement = xi_fitness - original_fitness

#     if verbose:
#         print(f"局部搜索完成:")
#         print(f"  - 迭代次数: {iteration}")
#         print(f"  - 总用时: {end_time - start_time:.2f}秒")
#         print(f"  - 适应度提升: {total_improvement:.6f}")
#         print(f"  - 最终适应度: {xi_fitness:.6f}")

#     return list(xi)

def local_search(xi, G, p, k=None, neighbors=None, max_hop=5,
                 max_iterations=50, convergence_threshold=1e-6,
                 parallel_threshold=20, verbose=False):
    """
    优化的局部搜索（进程级并行 + 向量化 PRE）
    """
    start_time = time.time()

    if neighbors is None:
        neighbors = {v: list(G.neighbors(v)) for v in G.nodes()}

    if not isinstance(xi, set):
        xi = set(xi)

    xi_fitness = PRE(G, xi, p, neighbors, max_hop)
    original_fitness = xi_fitness

    if verbose:
        print(f"局部搜索开始，初始适应度: {xi_fitness:.6f}")

    iteration = 0
    consecutive_no_improvement = 0
    max_no_improvement = 5

    while iteration < max_iterations and consecutive_no_improvement < max_no_improvement:
        iteration += 1

        def generate_candidates():
            for x_ij in xi:
                for neighbor in neighbors.get(x_ij, []):
                    if neighbor not in xi:
                        xi_new = xi.copy()
                        xi_new.remove(x_ij)
                        xi_new.add(neighbor)
                        yield (xi_new, x_ij, neighbor)

        candidates = list(generate_candidates())
        if not candidates:
            break

        if verbose:
            print(f"第{iteration}轮：评估{len(candidates)}个候选解")

        if len(candidates) >= parallel_threshold:
            try:
                results = Parallel(n_jobs=-1, backend='threading')(
                    delayed(PRE)(G, cand[0], p, neighbors, max_hop) for cand in candidates
                )
            except Exception as e:
                if verbose:
                    print(f"并行计算失败，回退到串行计算: {e}")
                results = [PRE(G, cand[0], p, neighbors, max_hop) for cand in candidates]
        else:
            results = [PRE(G, cand[0], p, neighbors, max_hop) for cand in candidates]

        # 找到最优候选
        best_idx = max(range(len(results)), key=lambda i: results[i])
        best_fit = results[best_idx]
        improvement = best_fit - xi_fitness

        if improvement > convergence_threshold:
            xi = candidates[best_idx][0]
            xi_fitness = best_fit
            consecutive_no_improvement = 0
            if verbose:
                print(f"第{iteration}轮：找到改进解 {xi_fitness:.6f} (提升 {improvement:.6f})")
        else:
            consecutive_no_improvement += 1
            if verbose:
                print(f"第{iteration}轮：无显著改进 (最佳提升 {improvement:.6f})")

    end_time = time.time()
    if verbose:
        print(f"完成，耗时 {end_time-start_time:.2f}s，提升 {xi_fitness-original_fitness:.6f}")

    return list(xi)
